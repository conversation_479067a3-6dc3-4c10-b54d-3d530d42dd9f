<template>
    <div class="right-pane">
        <div class="property-panel">
            <!-- 模板属性 -->
            <div class="panel-header">
                <h4>模板属性</h4>
            </div>
            <el-row>
                <el-form :model="sharedAddForm" :rules="rules" ref="addForm" label-width="100px" label-position="top"
                    class="property-form">
                    <el-form-item prop="name">
                        <el-col :span="6">
                            模板名称
                        </el-col>
                        <el-col :span="18">
                            <el-input v-model="sharedAddForm.name" placeholder="请输入模板名称"
                                @input="updateForm('name', $event)"></el-input>
                        </el-col>

                    </el-form-item>

                    <el-form-item prop="resolution_ratio" required>
                        <el-col :span="6">
                            分辨率
                        </el-col>
                        <el-col :span="18">
                            <el-select v-model="sharedAddForm.resolution_ratio" placeholder="请选择分辨率"
                                style="width: 100%;" @change="updateForm('resolution_ratio', $event)">
                                <el-option v-for="item in resolutionRatioList" :key="item.id" :label="item.name"
                                    :value="item.name"></el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <!-- <el-col :span="8">
                        切换时间(秒)
                    </el-col>
                    <el-col :span="15">
                        <el-form-item prop="swipter_time">
                            <el-input-number v-model="sharedAddForm.swipter_time" :min="-1" :max="3600" :step="1"
                                placeholder="自动切换时间" style="width: 100%;" @change="updateForm('swipter_time', $event)"
                                size="mini" controls-position="right" :precision="0" :controls="true"></el-input-number>
                            <div class="form-tip">-1表示不自动切换</div>
                        </el-form-item>
                    </el-col> -->
                </el-form>
            </el-row>

            <div v-if="selectedMaterial">
                <!-- 区域属性 -->
                <div>
                    <div class="panel-header" style="margin-top: 20px;">
                        <h4>区域属性</h4>
                    </div>
                    <el-form label-width="100px" label-position="top" class="property-form">
                        <el-row :gutter="2">
                            <el-col :span="2">
                                <el-form-item>
                                    左
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item>
                                    <el-input v-model.number="selectedMaterial.x_axis"
                                        @change="updateMaterialProperty('x_axis', $event)" size="mini"
                                        style="width: 100%;"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="2">
                                <el-form-item>
                                    上
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item>
                                    <el-input v-model.number="selectedMaterial.y_axis"
                                        @change="updateMaterialProperty('y_axis', $event)" size="mini"
                                        controls-position="right" style="width: 100%;"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="2">
                            <el-col :span="2">
                                <el-form-item>
                                    宽
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item>
                                    <el-input v-model.number="selectedMaterial.width"
                                        @change="updateMaterialProperty('width', $event)" size="mini"
                                        controls-position="right" style="width: 100%;"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="2">
                                <el-form-item>
                                    高
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item>
                                    <el-input v-model.number="selectedMaterial.height"
                                        @change="updateMaterialProperty('height', $event)" size="mini"
                                        controls-position="right" style="width: 100%;"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>

                <el-row>
                    <!-- 素材列表 -->
                    <div class="panel-header" style="margin-top: 20px;">
                        <h4>当前区域素材</h4>
                    </div>

                    <!-- 多文件列表模式 -->
                    <div class="multi-file-list" :key="multiFileListKey">
                        <el-table :data="multiFileMaterials" row-key="clientKey" size="mini" stripe class="file-table">
                            <el-table-column prop="sort" label="排序" width="80" align="center">
                                <template slot-scope="scope">
                                    <el-button-group size="mini">
                                        <el-button type="text" @click="moveFileUp(scope.$index)"
                                            :disabled="scope.$index === 0" icon="el-icon-top"></el-button>
                                        <el-button type="text" @click="moveFileDown(scope.$index)"
                                            :disabled="scope.$index === multiFileMaterials.length - 1"
                                            icon="el-icon-bottom"></el-button>
                                    </el-button-group>
                                </template>
                            </el-table-column>

                            <el-table-column prop="interval_time" label="时间" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-input-number v-if="selectedMaterial.type !== 2"
                                        v-model="scope.row.interval_time" :min="0" :max="3600" :step="1" size="mini"
                                        @change="updateIntervalTime(scope.row, $event)" style="width: 90px;"
                                        controls-position="right" :precision="0" :controls="true">
                                    </el-input-number>
                                    <span v-else class="video-auto-text">自动</span>
                                </template>
                            </el-table-column>

                            <el-table-column prop="sm_name" label="文件" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span class="file-name" :title="scope.row.sm_name">{{ scope.row.sm_name }}</span>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" width="60" align="center">
                                <template slot-scope="scope">
                                    <el-tooltip content="删除" placement="top">
                                        <el-button type="text" @click="deleteMaterialByClientKey(scope.row.clientKey)"
                                            class="delete-btn" icon="el-icon-delete" size="mini"></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                </el-row>
            </div>
            <!-- 图层排序 -->
            <div v-else class="layer-panel">
                <div class="panel-header" style="margin-top: 20px;">
                    <h4>图层顺序</h4>
                </div>
                <div v-if="currentPageMaterials.length > 0" class="layer-list">
                    <div v-for="(material, index) in currentPageMaterials" :key="material.clientKey" class="layer-item"
                        @click="highlightMaterial(index)">
                        <div class="layer-info">
                            <i :class="getMaterialIcon(material)" class="layer-icon"></i>
                            <span class="layer-name" :title="getMaterialName(material)">{{ getMaterialName(material)
                                }}</span>
                        </div>
                        <div class="layer-actions">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-arrow-up" :disabled="index === 0"
                                    @click.stop="moveLayer('up', index)"></el-button>
                                <el-button size="mini" icon="el-icon-arrow-down"
                                    :disabled="index === currentPageMaterials.length - 1"
                                    @click.stop="moveLayer('down', index)">
                                </el-button>
                            </el-button-group>
                        </div>
                    </div>
                </div>
                <el-empty v-else description="当前页面没有素材"></el-empty>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    name: 'TemplatePropertiesPanel',
    props: {
        sharedAddForm: {
            type: Object,
            required: true
        },
        rules: {
            type: Object,
            required: true
        },
        resolutionRatioList: {
            type: Array,
            required: true
        },
        currentPageMaterials: {
            type: Array,
            required: true
        },
        selectedMaterial: {
            type: Object,
            default: null
        },
        selectedMaterialIndex: {
            type: Number,
            default: -1
        }
    },
    data () {
        return {
            // 添加一个响应式的刷新触发器
            refreshTrigger: 0
        };
    },
    computed: {
        // Use a computed property for the key to react to changes in selectedMaterial
        materialListKey () {
            if (this.selectedMaterial) {
                return this.selectedMaterial.clientKey;
            }
            return 'no-selection';
        },
        // 多文件列表的响应式key，确保排序后强制重新渲染
        multiFileListKey () {
            if (!this.selectedMaterial || !this.selectedMaterial.multiFiles) {
                return 'no-multi-files';
            }
            // 基于文件顺序生成key，排序后key会变化，强制重新渲染
            const fileOrder = this.selectedMaterial.multiFiles.map(f => f.clientKey).join('-');
            return `${this.selectedMaterial.clientKey}-${fileOrder}`;
        },
        // 获取当前区域的多文件素材列表
        multiFileMaterials () {
            // 依赖refreshTrigger确保视图刷新
            this.refreshTrigger; // eslint-disable-line no-unused-expressions

            if (!this.selectedMaterial) {
                console.log('multiFileMaterials: no selected material');
                return [];
            }

            console.log('multiFileMaterials getter called', this.selectedMaterial.clientKey, 'refreshTrigger:', this.refreshTrigger);

            // 如果有multiFiles属性，直接使用它
            if (this.selectedMaterial.multiFiles && Array.isArray(this.selectedMaterial.multiFiles) && this.selectedMaterial.multiFiles.length > 0) {
                console.log('Returning multiFiles:', this.selectedMaterial.multiFiles.length, 'items');
                // 创建新数组确保响应式更新
                return [...this.selectedMaterial.multiFiles];
            }

            // 否则查找与当前选中素材在同一位置的所有素材（向后兼容）
            const fallbackMaterials = this.currentPageMaterials.filter(material =>
                material.x_axis === this.selectedMaterial.x_axis &&
                material.y_axis === this.selectedMaterial.y_axis &&
                material.type === this.selectedMaterial.type
            );
            console.log('Returning fallback materials:', fallbackMaterials.length, 'items');
            return fallbackMaterials;
        }
    },
    watch: {
        currentPageMaterials: {
            handler () {
                // 当素材列表发生变化时，触发刷新
                this.refreshTrigger++;
                // 移除 $forceUpdate() 避免重置输入框状态
            },
            deep: true,
            immediate: false
        },
        selectedMaterial: {
            handler () {
                // 当选中的素材发生变化时，触发刷新
                this.refreshTrigger++;
                // 移除 $forceUpdate() 避免重置输入框状态
            },
            deep: true,
            immediate: false
        }
    },
    methods: {
        updateMaterialProperty (key, value) {
            if (!this.selectedMaterial) return;
            this.$emit('update-material-property', {
                clientKey: this.selectedMaterial.clientKey,
                key: key,
                value: value
            });
        },
        updateForm (key, value) {
            this.$emit('update:sharedAddForm', { ...this.sharedAddForm, [key]: value });
        },
        deleteMaterial () {
            if (this.selectedMaterialIndex !== -1) {
                this.$emit('delete-material', this.selectedMaterialIndex);
            }
        },
        moveLayer (direction, index) {
            const targetIndex = this.selectedMaterialIndex !== -1 ? this.selectedMaterialIndex : index;
            this.$emit('move-layer', { index: targetIndex, direction });
        },
        highlightMaterial (index) {
            this.$emit('highlight-material', index);
        },
        getMaterialIcon (item) {
            if (item.template_sm_type === 2) return 'el-icon-time';
            if (item.template_sm_type === 5) return 'el-icon-link';
            if (item.type === 1) return 'el-icon-picture-outline';
            if (item.type === 2) return 'el-icon-video-camera';
            return 'el-icon-document';
        },
        getMaterialName (item) {
            if (item.template_sm_type === 2) return '时间组件';
            if (item.template_sm_type === 5) return '网页组件';
            return item.sm_name || '未命名素材';
        },
        // 检查是否为多文件模式
        isMultiFileMode () {
            if (!this.selectedMaterial) return false;


            // 检查是否有multiFiles属性
            if (this.selectedMaterial.multiFiles && this.selectedMaterial.multiFiles.length > 1) {

                return true;
            }

            // 向后兼容：检查是否有多个同类型的素材在同一位置
            const samePositionMaterials = this.currentPageMaterials.filter(material =>
                material.x_axis === this.selectedMaterial.x_axis &&
                material.y_axis === this.selectedMaterial.y_axis &&
                material.type === this.selectedMaterial.type
            );

            const result = samePositionMaterials.length > 1;

            return result;
        },
        // 获取素材数量文本
        getMaterialCountText () {
            if (!this.selectedMaterial) return '';

            // 如果有multiFiles属性
            if (this.selectedMaterial.multiFiles && this.selectedMaterial.multiFiles.length > 0) {
                return `${this.selectedMaterial.multiFiles.length} 个文件`;
            }

            // 向后兼容：检查是否有多个同类型的素材在同一位置
            const samePositionMaterials = this.currentPageMaterials.filter(material =>
                material.x_axis === this.selectedMaterial.x_axis &&
                material.y_axis === this.selectedMaterial.y_axis &&
                material.type === this.selectedMaterial.type
            );

            if (samePositionMaterials.length > 1) {
                return `${samePositionMaterials.length} 个文件`;
            }

            return this.selectedMaterial.sm_name || '未命名';
        },
        // 更新间隔时间
        updateIntervalTime (material, newTime) {
            console.log('updateIntervalTime called', material, newTime);

            if (this.selectedMaterial && this.selectedMaterial.multiFiles) {
                // 更新multiFiles中的间隔时间
                const fileIndex = this.selectedMaterial.multiFiles.findIndex(file => file.clientKey === material.clientKey);
                if (fileIndex !== -1) {
                    this.$set(this.selectedMaterial.multiFiles[fileIndex], 'interval_time', newTime);
                    console.log('Updated interval_time to:', newTime);
                    // 触发refreshTrigger更新，避免使用$forceUpdate()
                    this.refreshTrigger++;
                    // 通知轮播组件更新时间间隔
                    this.$emit('update-carousel-interval', this.selectedMaterial.clientKey);
                }
            } else {
                // 向后兼容：使用事件发射
                this.$emit('update-interval-time', { material, interval_time: newTime });
            }
        },
        // 根据clientKey删除素材
        deleteMaterialByClientKey (clientKey) {
            console.log('deleteMaterialByClientKey called', clientKey);

            if (this.selectedMaterial && this.selectedMaterial.multiFiles) {
                // 从multiFiles中删除
                const fileIndex = this.selectedMaterial.multiFiles.findIndex(file => file.clientKey === clientKey);
                if (fileIndex !== -1) {
                    console.log('Deleting material at index', fileIndex);

                    // 删除指定索引的素材
                    this.selectedMaterial.multiFiles.splice(fileIndex, 1);

                    // 根据剩余素材数量决定如何处理
                    if (this.selectedMaterial.multiFiles.length === 1) {
                        // 如果只剩一个文件，移除multiFiles属性
                        const lastFile = this.selectedMaterial.multiFiles[0];
                        this.$set(this.selectedMaterial, 'path', lastFile.path);
                        this.$set(this.selectedMaterial, 'sm_name', lastFile.sm_name);
                        this.$set(this.selectedMaterial, 'sm_id', lastFile.sm_id);
                        this.$delete(this.selectedMaterial, 'multiFiles');
                        console.log('Converted to single file mode');
                    } else if (this.selectedMaterial.multiFiles.length > 1) {
                        // 更新显示名称
                        this.$set(this.selectedMaterial, 'sm_name', `${this.selectedMaterial.multiFiles.length}个文件轮播`);

                        // 更新显示的图片为第一个文件
                        const firstFile = this.selectedMaterial.multiFiles[0];
                        this.$set(this.selectedMaterial, 'path', firstFile.path);
                        this.$set(this.selectedMaterial, 'sm_id', firstFile.sm_id);
                        console.log('Updated to multi-file mode with', this.selectedMaterial.multiFiles.length, 'files');
                    } else {
                        // 如果没有文件了，清除相关属性
                        this.$set(this.selectedMaterial, 'path', '');
                        this.$set(this.selectedMaterial, 'sm_name', '空区域');
                        this.$set(this.selectedMaterial, 'sm_id', 0);
                        this.$delete(this.selectedMaterial, 'multiFiles');
                        console.log('No files remaining, cleared material data');
                    }

                    // 触发refreshTrigger更新，强制计算属性重新计算
                    this.refreshTrigger++;
                    console.log('deleteMaterialByClientKey: refreshTrigger updated to', this.refreshTrigger);

                    // 重置轮播索引
                    this.$emit('reset-carousel-index', this.selectedMaterial.clientKey);
                } else {
                    console.log('Material not found with clientKey:', clientKey);
                }
            } else {
                // 向后兼容：从整个素材列表中删除
                console.log('Using fallback delete method');
                const index = this.currentPageMaterials.findIndex(material => material.clientKey === clientKey);
                if (index !== -1) {
                    this.$emit('delete-material', index);
                } else {
                    console.log('Material not found in currentPageMaterials with clientKey:', clientKey);
                }
            }
        },
        // 文件上移
        moveFileUp (index) {
            if (this.selectedMaterial && this.selectedMaterial.multiFiles && index > 0) {
                const newFiles = [...this.selectedMaterial.multiFiles];
                [newFiles[index], newFiles[index - 1]] = [newFiles[index - 1], newFiles[index]];

                console.log('moveFileUp: 排序前', this.selectedMaterial.multiFiles.map(f => f.sm_name));
                console.log('moveFileUp: 排序后', newFiles.map(f => f.sm_name));

                // 立即更新本地数据，确保响应式更新
                this.$set(this.selectedMaterial, 'multiFiles', newFiles);

                // 触发refreshTrigger更新，强制计算属性重新计算
                this.refreshTrigger++;
                console.log('moveFileUp: refreshTrigger updated to', this.refreshTrigger);

                // Emit an event to the parent component to handle the update
                this.$emit('update-multi-files', {
                    clientKey: this.selectedMaterial.clientKey,
                    files: newFiles
                });

                // Request the parent to reset the carousel's state
                this.$emit('reset-carousel-index', this.selectedMaterial.clientKey);
            }
        },
        // 文件下移
        moveFileDown (index) {
            if (this.selectedMaterial && this.selectedMaterial.multiFiles) {
                const newFiles = [...this.selectedMaterial.multiFiles];
                if (index < newFiles.length - 1) {
                    [newFiles[index], newFiles[index + 1]] = [newFiles[index + 1], newFiles[index]];

                    console.log('moveFileDown: 排序前', this.selectedMaterial.multiFiles.map(f => f.sm_name));
                    console.log('moveFileDown: 排序后', newFiles.map(f => f.sm_name));

                    // 立即更新本地数据，确保响应式更新
                    this.$set(this.selectedMaterial, 'multiFiles', newFiles);

                    // 触发refreshTrigger更新，强制计算属性重新计算
                    this.refreshTrigger++;
                    console.log('moveFileDown: refreshTrigger updated to', this.refreshTrigger);

                    // Emit an event to the parent component to handle the update
                    this.$emit('update-multi-files', {
                        clientKey: this.selectedMaterial.clientKey,
                        files: newFiles
                    });

                    // Request the parent to reset the carousel's state
                    this.$emit('reset-carousel-index', this.selectedMaterial.clientKey);
                }
            }
        },
        // 强制刷新当前区域素材数据
        refreshCurrentMaterialData () {
            console.log('refreshCurrentMaterialData called');

            if (this.selectedMaterial) {
                console.log('Refreshing data for selected material:', this.selectedMaterial.clientKey);

                // 触发refreshTrigger更新，强制计算属性重新计算
                this.refreshTrigger++;
                console.log('refreshCurrentMaterialData: refreshTrigger updated to', this.refreshTrigger);

                // 触发 multiFileMaterials 计算属性重新计算
                const currentMaterials = this.multiFileMaterials;
                console.log('Current multiFileMaterials:', currentMaterials);
                console.log('refreshCurrentMaterialData: view updated');
            } else {
                console.log('No selected material to refresh');
            }
        },

        validate (callback) {
            return this.$refs.addForm.validate(callback);
        }
    }
}
</script>

<style scoped lang="scss">
.right-pane {
    width: 100%;
    background: #fff;
    border-left: 1px solid #e6e6e6;
    overflow-y: auto;
    height: 100%;
}

.property-panel {
    padding: 8px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;

    h4 {
        margin: 0;
        font-size: 14px;
        color: #303133;
        font-weight: 600;
    }

    .material-count {
        font-size: 12px;
        color: #909399;
        background: #f0f2f5;
        padding: 2px 8px;
        border-radius: 10px;
    }
}

.property-form .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}

.property-form .el-row .el-form-item {
    margin-bottom: 1px;

}

.compact-form .el-form-item {
    margin-bottom: 8px;
}

::v-deep .compact-form .el-form-item__label {
    padding-right: 8px;
    font-size: 12px;
}

.multi-file-list {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;

    .file-table {
        width: 100%;

        ::v-deep .el-table__header th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
            font-size: 12px;
        }

        ::v-deep .el-table__row {
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f8f9fa !important;
            }
        }
    }
}

.video-auto-text {
    color: #909399;
    font-size: 12px;
    display: inline-block;
    width: 70px;
    text-align: center;
    line-height: 28px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.layer-panel {
    .layer-list {
        .layer-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 6px;
            background-color: #f5f7fa;
            cursor: pointer;
            border: 1px solid #e4e7ed;
            transition: all 0.2s ease;

            &:hover {
                background-color: #ecf5ff;
                border-color: #b3d8ff;
            }

            &.is-active {
                background-color: #409eff;
                color: #fff;
                border-color: #409eff;

                .layer-icon,
                .layer-name {
                    color: #fff;
                }
            }

            .layer-info {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
                overflow: hidden;
            }

            .layer-icon {
                font-size: 16px;
                color: #606266;
            }

            .layer-name {
                font-size: 13px;
                color: #303133;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .layer-actions {
                display: flex;
            }
        }
    }
}
</style>
