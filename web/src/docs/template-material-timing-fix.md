# 模板素材定时时间重置问题修复

## 问题描述

在模板编辑功能中，当删除一个素材后，剩余素材的定时时间（interval_time）会从原来设置的值（如7秒）重置为0秒。

## 问题根本原因

问题的根本原因是在 `TemplatePropertiesPanel.vue` 组件中过度使用了 `$forceUpdate()` 方法。当素材列表发生变化时（如删除素材），组件会调用 `$forceUpdate()` 强制重新渲染整个组件，这会导致：

1. **输入框状态丢失**：`el-input-number` 组件被重新创建，丢失了用户输入的定时时间值
2. **数据不一致**：虽然底层数据可能是正确的，但UI组件重新渲染时显示了默认值

### 具体触发路径

1. 用户删除素材 → `delMaterial()` 方法被调用
2. `delMaterial()` 更新素材列表 → 触发 `currentPageMaterials` watcher
3. Watcher 调用 `$forceUpdate()` → 整个组件重新渲染
4. `el-input-number` 组件重新创建 → 显示默认值而不是实际的 `interval_time`

## 修复方案

### 1. 移除不必要的 `$forceUpdate()` 调用

在 `TemplatePropertiesPanel.vue` 中：

```javascript
// 修复前
watch: {
    currentPageMaterials: {
        handler () {
            this.refreshTrigger++;
            this.$nextTick(() => {
                this.$forceUpdate(); // 移除这行
            });
        },
        deep: true,
        immediate: false
    }
}

// 修复后
watch: {
    currentPageMaterials: {
        handler () {
            this.refreshTrigger++;
            // 移除 $forceUpdate() 避免重置输入框状态
        },
        deep: true,
        immediate: false
    }
}
```

### 2. 优化 `updateIntervalTime` 方法

```javascript
// 修复前
updateIntervalTime (material, newTime) {
    this.$set(this.selectedMaterial.multiFiles[fileIndex], 'interval_time', newTime);
    this.$forceUpdate(); // 移除
    this.$nextTick(() => {
        this.$forceUpdate(); // 移除
    });
}

// 修复后
updateIntervalTime (material, newTime) {
    this.$set(this.selectedMaterial.multiFiles[fileIndex], 'interval_time', newTime);
    this.refreshTrigger++; // 使用refreshTrigger替代$forceUpdate
}
```

### 3. 依赖响应式系统

修复后的方案依赖Vue的响应式系统：
- 使用 `refreshTrigger` 触发计算属性重新计算
- 使用 `$set` 确保数据变化被正确检测
- 避免强制重新渲染整个组件

## 修复效果

修复后：
1. **定时时间保持不变**：删除素材后，剩余素材的定时时间不会被重置
2. **更好的性能**：减少不必要的组件重新渲染
3. **更稳定的用户体验**：输入框状态得到保持

## 测试步骤

1. 打开模板编辑页面
2. 添加多个图片素材到同一个区域
3. 设置每个素材的定时时间为不同的值（如7秒、10秒等）
4. 删除其中一个素材
5. **验证**：剩余素材的定时时间应该保持原来设置的值，不会变为0

## 技术要点

1. **避免过度使用 `$forceUpdate()`**：只在必要时使用，优先依赖Vue的响应式系统
2. **使用 `refreshTrigger` 模式**：通过响应式变量触发计算属性重新计算
3. **保持数据一致性**：确保UI显示与底层数据保持一致

## 相关文件

- `web/src/components/TemplatePropertiesPanel.vue`
- `web/src/views/Template.vue`
